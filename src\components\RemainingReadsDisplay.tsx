import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '../contexts/ThemeContext';
import { useLanguageNavigate } from '../hooks/useLanguageNavigate';
import { getBrowserFingerprint } from '../utils/fingerprint';
import { checkAnonymousEligibility } from '../services/anonymousService';
import { getFontClass } from '../utils/fontUtils';

interface User {
  vipStatus: string;
  remainingReads: number;
}

interface RemainingReadsDisplayProps {
  user: User | null;
  className?: string;
}

const RemainingReadsDisplay: React.FC<RemainingReadsDisplayProps> = ({
  user,
  className = ''
}) => {
  const { t, i18n } = useTranslation();
  const { theme } = useTheme();
  const { navigate } = useLanguageNavigate();
  const [anonymousReadsUsed, setAnonymousReadsUsed] = useState(false);
  const [loading, setLoading] = useState(true);
  const isDark = theme === 'dark';

  // 检查匿名用户状态
  useEffect(() => {
    const checkAnonymousStatus = async () => {
      if (!user) {
        try {
          const fingerprint = await getBrowserFingerprint();
          const eligibility = await checkAnonymousEligibility(fingerprint);
          setAnonymousReadsUsed(eligibility.hasUsed);
        } catch (error) {
          console.error('检查匿名用户状态失败:', error);
          setAnonymousReadsUsed(false);
        }
      }
      setLoading(false);
    };

    checkAnonymousStatus();
  }, [user]);

  const handleLoginClick = () => {
    // 直接跳转到登录页面
    navigate('/login');
  };

  if (loading) {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className={`animate-pulse w-48 h-10 rounded-full ${isDark ? 'bg-gray-700' : 'bg-gray-200'}`}></div>
      </div>
    );
  }

  if (!user) {
    // 未登录状态 - 无论剩余次数是多少，都显示登录按钮
    const remainingCount = anonymousReadsUsed ? 0 : 1;

    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${getFontClass(i18n.language)}
          ${isDark
            ? 'bg-purple-900/60 text-purple-100'
            : 'bg-purple-100 text-purple-700'
          }`}>
          <svg
            className={`w-4 h-4 ${isDark ? 'text-purple-200' : 'text-purple-700'}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M13 10V3L4 14h7v7l9-11h-7z"
            />
          </svg>
          <span>
            {t('common.remaining_reads', '剩余占卜次数：{{count}}次', { count: remainingCount })}
          </span>
          <button
            onClick={handleLoginClick}
            className={`ml-2 px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${
              isDark
                ? 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-md hover:shadow-purple-500/20'
                : 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white shadow-md hover:shadow-purple-500/20'
            }`}
          >
            {t('anonymous.login_for_more', '登录获取更多')}
          </button>
        </div>
      </div>
    );
  }
  
  // 已登录状态
  if (user.vipStatus === 'active') {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${getFontClass(i18n.language)}
          ${isDark
            ? 'bg-purple-900/60 text-purple-100'
            : 'bg-purple-100 text-purple-700'
          }`}>
          <svg
            className={`w-4 h-4 ${isDark ? 'text-purple-200' : 'text-purple-700'}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M13 10V3L4 14h7v7l9-11h-7z"
            />
          </svg>
          <span>
            {t('common.remaining_reads_vip', 'VIP用户 - 无限次占卜')}
          </span>
        </div>
      </div>
    );
  }

  const remainingReads = Math.max(0, user.remainingReads);

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${getFontClass(i18n.language)}
        ${isDark
          ? 'bg-purple-900/60 text-purple-100'
          : 'bg-purple-100 text-purple-700'
        }`}>
        <svg
          className={`w-4 h-4 ${isDark ? 'text-purple-200' : 'text-purple-700'}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M13 10V3L4 14h7v7l9-11h-7z"
          />
        </svg>
        <span>
          {t('common.remaining_reads', '剩余占卜次数：{{count}}次', { count: remainingReads })}
        </span>
        {/* 普通用户显示解锁按钮 */}
        <button
          onClick={() => navigate('/membership')}
          className={`ml-2 px-3 py-1.5 rounded-full text-xs font-medium transition-all duration-200 ${
            isDark
              ? 'bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-md hover:shadow-purple-500/20'
              : 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white shadow-md hover:shadow-purple-500/20'
          }`}
        >
          {t('user.unlock_more_reads', '解锁更多占卜次数')}
        </button>
      </div>
    </div>
  );
};

export default RemainingReadsDisplay;
